/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/activity/route";
exports.ids = ["app/api/activity/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_activity_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/activity/route.ts */ \"(rsc)/./src/app/api/activity/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/activity/route\",\n        pathname: \"/api/activity\",\n        filename: \"route\",\n        bundlePath: \"app/api/activity/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\activity\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_activity_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/activity/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/activity/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        if (!supabase) {\n            console.error('Supabase client could not be initialized in /api/activity.');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error.'\n            }, {\n                status: 500\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const limit = parseInt(searchParams.get('limit') || '10');\n        // Get recent request logs for activity feed\n        const { data: logs, error: logsError } = await supabase.from('request_logs').select(`\n        id,\n        request_timestamp,\n        status_code,\n        llm_model_name,\n        llm_provider_name,\n        error_message,\n        cost,\n        input_tokens,\n        output_tokens,\n        custom_api_config_id\n      `).order('request_timestamp', {\n            ascending: false\n        }).limit(limit);\n        if (logsError) {\n            console.error('Error fetching activity logs:', logsError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch activity data'\n            }, {\n                status: 500\n            });\n        }\n        // Get recent API key additions\n        const { data: apiKeys, error: keysError } = await supabase.from('api_keys').select(`\n        id,\n        created_at,\n        label,\n        provider,\n        predefined_model_id\n      `).order('created_at', {\n            ascending: false\n        }).limit(5);\n        if (keysError) {\n            console.error('Error fetching API keys:', keysError);\n        }\n        // Get recent custom config updates\n        const { data: configs, error: configsError } = await supabase.from('custom_api_configs').select(`\n        id,\n        updated_at,\n        name,\n        routing_strategy\n      `).order('updated_at', {\n            ascending: false\n        }).limit(5);\n        if (configsError) {\n            console.error('Error fetching configs:', configsError);\n        }\n        // Combine and format activity data\n        const activities = [];\n        // Add request activities\n        if (logs) {\n            logs.forEach((log)=>{\n                const isSuccess = log.status_code >= 200 && log.status_code < 300;\n                const isError = log.status_code >= 400;\n                let action = 'Request completed';\n                let status = 'success';\n                if (isError) {\n                    action = 'Request failed';\n                    status = 'error';\n                } else if (log.status_code >= 300) {\n                    action = 'Request redirected';\n                    status = 'warning';\n                }\n                activities.push({\n                    id: `request-${log.id}`,\n                    type: 'request',\n                    action,\n                    model: log.llm_model_name || 'Unknown Model',\n                    provider: log.llm_provider_name,\n                    timestamp: log.request_timestamp,\n                    status,\n                    details: log.error_message,\n                    cost: log.cost,\n                    tokens: log.input_tokens && log.output_tokens ? `${log.input_tokens} in, ${log.output_tokens} out` : null\n                });\n            });\n        }\n        // Add API key activities\n        if (apiKeys) {\n            apiKeys.forEach((key)=>{\n                activities.push({\n                    id: `key-${key.id}`,\n                    type: 'api_key',\n                    action: 'New API key added',\n                    model: key.predefined_model_id || key.provider,\n                    provider: key.provider,\n                    timestamp: key.created_at,\n                    status: 'info',\n                    details: key.label\n                });\n            });\n        }\n        // Add config activities\n        if (configs) {\n            configs.forEach((config)=>{\n                activities.push({\n                    id: `config-${config.id}`,\n                    type: 'config',\n                    action: 'Configuration updated',\n                    model: config.name,\n                    provider: config.routing_strategy,\n                    timestamp: config.updated_at,\n                    status: 'info',\n                    details: `Routing strategy: ${config.routing_strategy}`\n                });\n            });\n        }\n        // Sort by timestamp and limit\n        activities.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n        const limitedActivities = activities.slice(0, limit);\n        // Format timestamps\n        const formattedActivities = limitedActivities.map((activity)=>({\n                ...activity,\n                time: getTimeAgo(new Date(activity.timestamp))\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            activities: formattedActivities,\n            total: activities.length\n        });\n    } catch (error) {\n        console.error('Error in /api/activity:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/activity/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();