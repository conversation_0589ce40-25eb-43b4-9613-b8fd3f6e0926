"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SignInPageContent() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Password reset modal state\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetEmail, setResetEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetError, setResetError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPageContent.useEffect\": ()=>{\n            // TEMPORARILY DISABLED: Check if user is already signed in\n            // This is causing redirect loops, so we'll let users manually fill out the form\n            console.log('Signin page loaded, automatic session check disabled to prevent redirect loops');\n        /*\n    const checkUser = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      if (session) {\n        const redirectTo = searchParams.get('redirectTo');\n        const plan = searchParams.get('plan');\n\n        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {\n          router.push(`/pricing?plan=${plan}&checkout=true`);\n        } else if (redirectTo) {\n          router.push(redirectTo);\n        } else {\n          router.push('/dashboard');\n        }\n      }\n    };\n    checkUser();\n    */ }\n    }[\"SignInPageContent.useEffect\"], [\n        router,\n        searchParams,\n        supabase.auth\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                throw error;\n            }\n            if (data.user) {\n                console.log('Sign in successful, user:', data.user.id);\n                console.log('Current URL:', window.location.href);\n                console.log('Environment:', \"development\");\n                console.log('Site URL:', \"http://localhost:3000\");\n                // Wait a moment for session to be established\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // Handle redirect logic - users should be able to access dashboard regardless of subscription\n                const redirectTo = searchParams.get('redirectTo');\n                const plan = searchParams.get('plan');\n                const email = searchParams.get('email');\n                const checkoutUserId = searchParams.get('checkout_user_id');\n                console.log('Redirect params:', {\n                    redirectTo,\n                    plan,\n                    email,\n                    checkoutUserId\n                });\n                // Check actual subscription status in database first, then metadata\n                try {\n                    const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status').eq('id', data.user.id).single();\n                    // If user has active subscription in database, redirect to dashboard regardless of metadata\n                    if (profile && profile.subscription_status === 'active') {\n                        console.log('User has active subscription in database, redirecting to dashboard:', {\n                            userId: data.user.id,\n                            tier: profile.subscription_tier,\n                            status: profile.subscription_status\n                        });\n                        // Handle redirect logic for active users\n                        if (redirectTo) {\n                            router.push(redirectTo);\n                        } else {\n                            router.push('/dashboard');\n                        }\n                        return; // Exit early to prevent other redirects\n                    }\n                    // Only check metadata if no active subscription in database\n                    const userMetadata = data.user.user_metadata;\n                    const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n                    const userPlan = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.plan;\n                    if (paymentStatus === 'pending' && userPlan && [\n                        'starter',\n                        'professional',\n                        'enterprise'\n                    ].includes(userPlan)) {\n                        console.log('User has pending payment status and no active subscription, redirecting to pricing for fresh start:', {\n                            userId: data.user.id,\n                            plan: userPlan,\n                            paymentStatus,\n                            dbProfile: profile\n                        });\n                        // Redirect to pricing page for fresh signup process\n                        console.log('Redirecting pending payment user to pricing page');\n                        router.push('/pricing');\n                        return; // Exit early to prevent other redirects\n                    }\n                } catch (error) {\n                    console.error('Error checking user profile during sign-in:', error);\n                // On error, continue with normal flow\n                }\n                // If this is specifically a checkout flow, redirect to checkout\n                if (checkoutUserId && plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                    console.log('Redirecting to checkout:', checkoutUrl);\n                    router.push(checkoutUrl);\n                } else if (plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    // Check user's current subscription tier before redirecting to checkout\n                    // Free users should go to dashboard, not checkout, even if there's a plan parameter\n                    try {\n                        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status').eq('id', data.user.id).single();\n                        if (profile && profile.subscription_tier === 'free' && profile.subscription_status === 'active') {\n                            // Free user with plan parameter - redirect to dashboard instead of checkout\n                            console.log('Free user with plan parameter, redirecting to dashboard instead of checkout');\n                            router.push('/dashboard');\n                        } else {\n                            // Paid user or user without profile - redirect to checkout\n                            const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                            console.log('Redirecting to checkout for plan:', checkoutUrl);\n                            router.push(checkoutUrl);\n                        }\n                    } catch (error) {\n                        console.error('Error checking user profile for redirect:', error);\n                        // On error, default to dashboard to be safe\n                        router.push('/dashboard');\n                    }\n                } else if (redirectTo) {\n                    // Redirect to specified location\n                    console.log('Redirecting to specified location:', redirectTo);\n                    router.push(redirectTo);\n                } else {\n                    // Default redirect to dashboard - all users should be able to access dashboard\n                    console.log('Redirecting to dashboard');\n                    router.push('/dashboard');\n                }\n            }\n        } catch (err) {\n            console.error('Sign in error:', err);\n            setError(err.message || 'Invalid email or password. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePasswordReset = async (e)=>{\n        e.preventDefault();\n        setIsResetLoading(true);\n        setResetError('');\n        if (!resetEmail.trim()) {\n            setResetError('Please enter your email address');\n            setIsResetLoading(false);\n            return;\n        }\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                throw error;\n            }\n            success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n            setResetEmail('');\n        } catch (err) {\n            console.error('Password reset error:', err);\n            setResetError(err.message || 'Failed to send reset email. Please try again.');\n            toastError('Failed to send reset email', err.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const openResetModal = ()=>{\n        setResetEmail(email);\n        setResetError('');\n        setShowResetModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-lg relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"inline-flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/roukey_logo.png\",\n                                            alt: \"RouKey\",\n                                            width: 32,\n                                            height: 32,\n                                            className: \"w-8 h-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white leading-tight\",\n                                    children: \"Welcome back!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/80 leading-relaxed\",\n                                    children: \"Sign in to your RouKey account and continue building with our powerful routing platform.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signup\",\n                                                className: \"text-white font-medium hover:text-white/80 transition-colors\",\n                                                children: \"Sign up here\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center p-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/roukey_logo.png\",\n                                        alt: \"RouKey\",\n                                        width: 32,\n                                        height: 32,\n                                        className: \"w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"Welcome back to RouKey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? 'text' : 'password',\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                                    placeholder: \"Enter your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: openResetModal,\n                                        className: \"text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this) : 'Sign in'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/signup\",\n                                        className: \"text-blue-600 hover:text-blue-500 font-medium transition-colors\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handlePasswordReset,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"resetEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"resetEmail\",\n                                            type: \"email\",\n                                            required: true,\n                                            value: resetEmail,\n                                            onChange: (e)=>setResetEmail(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this),\n                                resetError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 text-sm\",\n                                        children: resetError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowResetModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isResetLoading,\n                                            className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                            children: isResetLoading ? 'Sending...' : 'Send Reset Email'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPageContent, \"EZp0z/+fgwBvgm/VvUKXKXxOpS8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SignInPageContent;\nfunction SignInPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 443,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignInPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 449,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 441,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SignInPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SignInPageContent\");\n$RefreshReg$(_c1, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signin/page.tsx\n"));

/***/ })

});