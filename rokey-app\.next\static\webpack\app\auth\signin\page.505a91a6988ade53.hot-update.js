"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SignInPageContent() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Password reset modal state\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetEmail, setResetEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetError, setResetError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPageContent.useEffect\": ()=>{\n            // TEMPORARILY DISABLED: Check if user is already signed in\n            // This is causing redirect loops, so we'll let users manually fill out the form\n            console.log('Signin page loaded, automatic session check disabled to prevent redirect loops');\n        /*\n    const checkUser = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      if (session) {\n        const redirectTo = searchParams.get('redirectTo');\n        const plan = searchParams.get('plan');\n\n        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {\n          router.push(`/pricing?plan=${plan}&checkout=true`);\n        } else if (redirectTo) {\n          router.push(redirectTo);\n        } else {\n          router.push('/dashboard');\n        }\n      }\n    };\n    checkUser();\n    */ }\n    }[\"SignInPageContent.useEffect\"], [\n        router,\n        searchParams,\n        supabase.auth\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                throw error;\n            }\n            if (data.user) {\n                console.log('Sign in successful, user:', data.user.id);\n                console.log('Current URL:', window.location.href);\n                console.log('Environment:', \"development\");\n                console.log('Site URL:', \"http://localhost:3000\");\n                // Wait a moment for session to be established\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // Handle redirect logic - users should be able to access dashboard regardless of subscription\n                const redirectTo = searchParams.get('redirectTo');\n                const plan = searchParams.get('plan');\n                const email = searchParams.get('email');\n                const checkoutUserId = searchParams.get('checkout_user_id');\n                console.log('Redirect params:', {\n                    redirectTo,\n                    plan,\n                    email,\n                    checkoutUserId\n                });\n                // Check actual subscription status in database first, then metadata\n                try {\n                    const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status').eq('id', data.user.id).single();\n                    // If user has active subscription in database, redirect to dashboard regardless of metadata\n                    if (profile && profile.subscription_status === 'active') {\n                        console.log('User has active subscription in database, redirecting to dashboard:', {\n                            userId: data.user.id,\n                            tier: profile.subscription_tier,\n                            status: profile.subscription_status\n                        });\n                        // Handle redirect logic for active users\n                        if (redirectTo) {\n                            router.push(redirectTo);\n                        } else {\n                            router.push('/dashboard');\n                        }\n                        return; // Exit early to prevent other redirects\n                    }\n                    // Only check metadata if no active subscription in database\n                    const userMetadata = data.user.user_metadata;\n                    const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n                    const userPlan = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.plan;\n                    if (paymentStatus === 'pending' && userPlan && [\n                        'starter',\n                        'professional',\n                        'enterprise'\n                    ].includes(userPlan)) {\n                        console.log('User has pending payment status and no active subscription, redirecting to pricing for fresh start:', {\n                            userId: data.user.id,\n                            plan: userPlan,\n                            paymentStatus,\n                            dbProfile: profile\n                        });\n                        // Redirect to pricing page for fresh signup process\n                        console.log('Redirecting pending payment user to pricing page');\n                        router.push('/pricing');\n                        return; // Exit early to prevent other redirects\n                    }\n                } catch (error) {\n                    console.error('Error checking user profile during sign-in:', error);\n                // On error, continue with normal flow\n                }\n                // If this is specifically a checkout flow, redirect to checkout\n                if (checkoutUserId && plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                    console.log('Redirecting to checkout:', checkoutUrl);\n                    router.push(checkoutUrl);\n                } else if (plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    // Check user's current subscription tier before redirecting to checkout\n                    // Free users should go to dashboard, not checkout, even if there's a plan parameter\n                    try {\n                        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status').eq('id', data.user.id).single();\n                        if (profile && profile.subscription_tier === 'free' && profile.subscription_status === 'active') {\n                            // Free user with plan parameter - redirect to dashboard instead of checkout\n                            console.log('Free user with plan parameter, redirecting to dashboard instead of checkout');\n                            router.push('/dashboard');\n                        } else {\n                            // Paid user or user without profile - redirect to checkout\n                            const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                            console.log('Redirecting to checkout for plan:', checkoutUrl);\n                            router.push(checkoutUrl);\n                        }\n                    } catch (error) {\n                        console.error('Error checking user profile for redirect:', error);\n                        // On error, default to dashboard to be safe\n                        router.push('/dashboard');\n                    }\n                } else if (redirectTo) {\n                    // Redirect to specified location\n                    console.log('Redirecting to specified location:', redirectTo);\n                    router.push(redirectTo);\n                } else {\n                    // Default redirect to dashboard - all users should be able to access dashboard\n                    console.log('Redirecting to dashboard');\n                    router.push('/dashboard');\n                }\n            }\n        } catch (err) {\n            console.error('Sign in error:', err);\n            setError(err.message || 'Invalid email or password. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePasswordReset = async (e)=>{\n        e.preventDefault();\n        setIsResetLoading(true);\n        setResetError('');\n        if (!resetEmail.trim()) {\n            setResetError('Please enter your email address');\n            setIsResetLoading(false);\n            return;\n        }\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                throw error;\n            }\n            success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n            setResetEmail('');\n        } catch (err) {\n            console.error('Password reset error:', err);\n            setResetError(err.message || 'Failed to send reset email. Please try again.');\n            toastError('Failed to send reset email', err.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const openResetModal = ()=>{\n        setResetEmail(email);\n        setResetError('');\n        setShowResetModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.03]\",\n                        style: {\n                            backgroundImage: 'linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)',\n                            backgroundSize: '60px 60px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-lg relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/RouKey_Logo_GLOW.png\",\n                                            alt: \"RouKey\",\n                                            width: 48,\n                                            height: 48,\n                                            className: \"w-12 h-12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold text-white leading-tight\",\n                                        children: \"Welcome back!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-white/80 leading-relaxed\",\n                                        children: \"Sign in to your RouKey account and continue building with our powerful routing platform.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: [\n                                                \"Don't have an account?\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"text-white font-medium hover:text-white/80 transition-colors\",\n                                                    children: \"Sign up here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center p-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/RouKey_Logo_NOGLOW.png\",\n                                        alt: \"RouKey\",\n                                        width: 32,\n                                        height: 32,\n                                        className: \"w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"Welcome back to RouKey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? 'text' : 'password',\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                                    placeholder: \"Enter your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: openResetModal,\n                                        className: \"text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this) : 'Sign in'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/signup\",\n                                        className: \"text-blue-600 hover:text-blue-500 font-medium transition-colors\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handlePasswordReset,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"resetEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"resetEmail\",\n                                            type: \"email\",\n                                            required: true,\n                                            value: resetEmail,\n                                            onChange: (e)=>setResetEmail(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this),\n                                resetError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 text-sm\",\n                                        children: resetError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowResetModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isResetLoading,\n                                            className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                            children: isResetLoading ? 'Sending...' : 'Send Reset Email'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 387,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPageContent, \"EZp0z/+fgwBvgm/VvUKXKXxOpS8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SignInPageContent;\nfunction SignInPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 448,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignInPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 447,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SignInPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SignInPageContent\");\n$RefreshReg$(_c1, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signin/page.tsx\n"));

/***/ })

});