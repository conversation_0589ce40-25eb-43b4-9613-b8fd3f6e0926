"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SignInPageContent() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Password reset modal state\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetEmail, setResetEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resetError, setResetError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPageContent.useEffect\": ()=>{\n            // TEMPORARILY DISABLED: Check if user is already signed in\n            // This is causing redirect loops, so we'll let users manually fill out the form\n            console.log('Signin page loaded, automatic session check disabled to prevent redirect loops');\n        /*\n    const checkUser = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      if (session) {\n        const redirectTo = searchParams.get('redirectTo');\n        const plan = searchParams.get('plan');\n\n        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {\n          router.push(`/pricing?plan=${plan}&checkout=true`);\n        } else if (redirectTo) {\n          router.push(redirectTo);\n        } else {\n          router.push('/dashboard');\n        }\n      }\n    };\n    checkUser();\n    */ }\n    }[\"SignInPageContent.useEffect\"], [\n        router,\n        searchParams,\n        supabase.auth\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                throw error;\n            }\n            if (data.user) {\n                console.log('Sign in successful, user:', data.user.id);\n                console.log('Current URL:', window.location.href);\n                console.log('Environment:', \"development\");\n                console.log('Site URL:', \"http://localhost:3000\");\n                // Wait a moment for session to be established\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // Handle redirect logic - users should be able to access dashboard regardless of subscription\n                const redirectTo = searchParams.get('redirectTo');\n                const plan = searchParams.get('plan');\n                const email = searchParams.get('email');\n                const checkoutUserId = searchParams.get('checkout_user_id');\n                console.log('Redirect params:', {\n                    redirectTo,\n                    plan,\n                    email,\n                    checkoutUserId\n                });\n                // Check actual subscription status in database first, then metadata\n                try {\n                    const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status').eq('id', data.user.id).single();\n                    // If user has active subscription in database, redirect to dashboard regardless of metadata\n                    if (profile && profile.subscription_status === 'active') {\n                        console.log('User has active subscription in database, redirecting to dashboard:', {\n                            userId: data.user.id,\n                            tier: profile.subscription_tier,\n                            status: profile.subscription_status\n                        });\n                        // Handle redirect logic for active users\n                        if (redirectTo) {\n                            router.push(redirectTo);\n                        } else {\n                            router.push('/dashboard');\n                        }\n                        return; // Exit early to prevent other redirects\n                    }\n                    // Only check metadata if no active subscription in database\n                    const userMetadata = data.user.user_metadata;\n                    const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n                    const userPlan = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.plan;\n                    if (paymentStatus === 'pending' && userPlan && [\n                        'starter',\n                        'professional',\n                        'enterprise'\n                    ].includes(userPlan)) {\n                        console.log('User has pending payment status and no active subscription, redirecting to pricing for fresh start:', {\n                            userId: data.user.id,\n                            plan: userPlan,\n                            paymentStatus,\n                            dbProfile: profile\n                        });\n                        // Redirect to pricing page for fresh signup process\n                        console.log('Redirecting pending payment user to pricing page');\n                        router.push('/pricing');\n                        return; // Exit early to prevent other redirects\n                    }\n                } catch (error) {\n                    console.error('Error checking user profile during sign-in:', error);\n                // On error, continue with normal flow\n                }\n                // If this is specifically a checkout flow, redirect to checkout\n                if (checkoutUserId && plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                    console.log('Redirecting to checkout:', checkoutUrl);\n                    router.push(checkoutUrl);\n                } else if (plan && [\n                    'starter',\n                    'professional',\n                    'enterprise'\n                ].includes(plan)) {\n                    // Check user's current subscription tier before redirecting to checkout\n                    // Free users should go to dashboard, not checkout, even if there's a plan parameter\n                    try {\n                        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status').eq('id', data.user.id).single();\n                        if (profile && profile.subscription_tier === 'free' && profile.subscription_status === 'active') {\n                            // Free user with plan parameter - redirect to dashboard instead of checkout\n                            console.log('Free user with plan parameter, redirecting to dashboard instead of checkout');\n                            router.push('/dashboard');\n                        } else {\n                            // Paid user or user without profile - redirect to checkout\n                            const checkoutUrl = \"/checkout?plan=\".concat(plan, \"&user_id=\").concat(data.user.id).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                            console.log('Redirecting to checkout for plan:', checkoutUrl);\n                            router.push(checkoutUrl);\n                        }\n                    } catch (error) {\n                        console.error('Error checking user profile for redirect:', error);\n                        // On error, default to dashboard to be safe\n                        router.push('/dashboard');\n                    }\n                } else if (redirectTo) {\n                    // Redirect to specified location\n                    console.log('Redirecting to specified location:', redirectTo);\n                    router.push(redirectTo);\n                } else {\n                    // Default redirect to dashboard - all users should be able to access dashboard\n                    console.log('Redirecting to dashboard');\n                    router.push('/dashboard');\n                }\n            }\n        } catch (err) {\n            console.error('Sign in error:', err);\n            setError(err.message || 'Invalid email or password. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePasswordReset = async (e)=>{\n        e.preventDefault();\n        setIsResetLoading(true);\n        setResetError('');\n        if (!resetEmail.trim()) {\n            setResetError('Please enter your email address');\n            setIsResetLoading(false);\n            return;\n        }\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                throw error;\n            }\n            success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n            setResetEmail('');\n        } catch (err) {\n            console.error('Password reset error:', err);\n            setResetError(err.message || 'Failed to send reset email. Please try again.');\n            toastError('Failed to send reset email', err.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const openResetModal = ()=>{\n        setResetEmail(email);\n        setResetError('');\n        setShowResetModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.15]\",\n                        style: {\n                            backgroundImage: 'linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px)',\n                            backgroundSize: '40px 40px',\n                            maskImage: 'radial-gradient(ellipse at center, black 30%, transparent 80%)',\n                            WebkitMaskImage: 'radial-gradient(ellipse at center, black 30%, transparent 80%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-lg relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/RouKey_Logo_GLOW.png\",\n                                            alt: \"RouKey\",\n                                            width: 48,\n                                            height: 48,\n                                            className: \"w-12 h-12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold text-white leading-tight\",\n                                        children: \"Welcome back!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-white/80 leading-relaxed\",\n                                        children: \"Sign in to your RouKey account and continue building with our powerful routing platform.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: [\n                                                \"Don't have an account?\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/auth/signup\",\n                                                    className: \"text-white font-medium hover:text-white/80 transition-colors\",\n                                                    children: \"Sign up here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center p-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/RouKey_Logo_NOGLOW.png\",\n                                        alt: \"RouKey\",\n                                        width: 32,\n                                        height: 32,\n                                        className: \"w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"Welcome back to RouKey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? 'text' : 'password',\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                                    placeholder: \"Enter your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: openResetModal,\n                                        className: \"text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this) : 'Sign in'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/signup\",\n                                        className: \"text-blue-600 hover:text-blue-500 font-medium transition-colors\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handlePasswordReset,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"resetEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"resetEmail\",\n                                            type: \"email\",\n                                            required: true,\n                                            value: resetEmail,\n                                            onChange: (e)=>setResetEmail(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this),\n                                resetError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 text-sm\",\n                                        children: resetError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowResetModal(false),\n                                            className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isResetLoading,\n                                            className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                            children: isResetLoading ? 'Sending...' : 'Send Reset Email'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPageContent, \"EZp0z/+fgwBvgm/VvUKXKXxOpS8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SignInPageContent;\nfunction SignInPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 451,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 450,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignInPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n            lineNumber: 457,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 449,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SignInPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SignInPageContent\");\n$RefreshReg$(_c1, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signin/page.tsx\n"));

/***/ })

});