"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"aaad12f411f3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFhYWQxMmY0MTFmM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx":
/*!****************************************************!*\
  !*** ./src/components/OptimisticLoadingScreen.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptimisticLoadingScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LoadingSkeleton */ \"(app-pages-browser)/./src/components/LoadingSkeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import skeleton components\n\n// Route configurations for different loading screens\nconst routeConfigs = {\n    '/dashboard': {\n        title: 'Dashboard',\n        subtitle: 'Loading overview & analytics...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: 'text-blue-500',\n        bgColor: 'bg-blue-50'\n    },\n    '/my-models': {\n        title: 'My Models',\n        subtitle: 'Loading API key management...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: 'text-green-500',\n        bgColor: 'bg-green-50'\n    },\n    '/playground': {\n        title: 'Playground',\n        subtitle: 'Loading model testing environment...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: 'text-orange-500',\n        bgColor: 'bg-orange-50'\n    },\n    '/routing-setup': {\n        title: 'Routing Setup',\n        subtitle: 'Loading routing configuration...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: 'text-purple-500',\n        bgColor: 'bg-purple-50'\n    },\n    '/logs': {\n        title: 'Logs',\n        subtitle: 'Loading request history...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: 'text-gray-500',\n        bgColor: 'bg-gray-50'\n    },\n    '/training': {\n        title: 'Prompt Engineering',\n        subtitle: 'Loading custom prompts...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: 'text-indigo-500',\n        bgColor: 'bg-indigo-50'\n    },\n    '/analytics': {\n        title: 'Analytics',\n        subtitle: 'Loading advanced insights...',\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: 'text-pink-500',\n        bgColor: 'bg-pink-50'\n    }\n};\nfunction OptimisticLoadingScreen(param) {\n    let { targetRoute } = param;\n    _s();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_2__.useNavigationSafe)();\n    const { clearNavigation } = navigationContext || {\n        clearNavigation: ()=>{}\n    };\n    // Get route config or default\n    const config = targetRoute ? routeConfigs[targetRoute] : null;\n    if (!config) {\n        // Generic loading screen for unknown routes\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[60vh]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Please wait while we load the page\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    }\n    // Simple inline skeleton components\n    const SimplePlaygroundSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 animate-fade-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded animate-pulse w-1/4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded animate-pulse w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-3\",\n                            children: Array.from({\n                                length: 5\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 bg-gray-200 rounded-lg animate-pulse\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-96 bg-gray-200 rounded-lg animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n            lineNumber: 106,\n            columnNumber: 5\n        }, this);\n    const SimpleLogsSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 animate-fade-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded animate-pulse w-1/4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded animate-pulse w-20\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded animate-pulse w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: Array.from({\n                        length: 8\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded-lg animate-pulse\"\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n            lineNumber: 125,\n            columnNumber: 5\n        }, this);\n    // Render the appropriate skeleton based on the target route\n    const renderSkeleton = ()=>{\n        switch(targetRoute){\n            case '/dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__.DashboardSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 16\n                }, this);\n            case '/my-models':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__.MyModelsSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 16\n                }, this);\n            case '/playground':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimplePlaygroundSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 16\n                }, this);\n            case '/routing-setup':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__.RoutingSetupSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 16\n                }, this);\n            case '/logs':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleLogsSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case '/training':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__.TrainingSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            case '/analytics':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__.AnalyticsSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_3__.DashboardSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this); // Fallback to dashboard skeleton\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: clearNavigation,\n                className: \"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                title: \"Cancel loading\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            renderSkeleton()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLoadingScreen.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimisticLoadingScreen, \"v5FB7+SddS7k6fvmU/2wcDEM650=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_2__.useNavigationSafe\n    ];\n});\n_c = OptimisticLoadingScreen;\nvar _c;\n$RefreshReg$(_c, \"OptimisticLoadingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx\n"));

/***/ })

});